# OEC末影箱插件

一个为Paper 1.8.8设计的Minecraft插件，允许玩家使用指令打开末影箱，但在战斗状态下会被阻止。

## 功能特性

- **指令访问**: 使用 `/oec` 指令随时打开末影箱
- **战斗限制**: 玩家在战斗状态下无法打开末影箱
- **战斗检测**: 自动检测玩家攻击和被攻击行为
- **时间控制**: 战斗状态持续10秒（可配置）
- **消息提示**: 屏幕中央显示剩余战斗时间
- **变量支持**: 支持自定义消息变量
- **配置文件**: 完全可配置的消息系统

## 安装方法

1. 下载 `OECPlugin-1.0.0-with-deps.jar` 文件
2. 将jar文件放入服务器的 `plugins` 文件夹
3. 重启服务器或使用 `/reload` 命令
4. 插件会自动生成配置文件

## 使用方法

### 基本指令
- `/oec` - 打开末影箱（需要权限 `oec.use`）

### 权限系统
- `oec.use` - 允许使用 `/oec` 指令（默认：所有玩家）
- `oec.admin` - 管理员权限（默认：OP）

## 配置文件

插件会在 `plugins/OECPlugin/` 目录下生成 `message.yml` 配置文件：

```yaml
messages:
  # 战斗状态中无法打开末影箱的提示消息
  combat-deny: "&c您当前{fight_time}秒内无法打开末影箱，因为你处于战斗状态"
  
  # 成功打开末影箱的消息
  enderchest-opened: "&a末影箱已打开"

combat:
  # 战斗状态持续时间（秒）
  duration: 10
```

### 支持的变量
- `{fight_time}` - 剩余战斗时间（秒）
- `{killer}` - 攻击者名称
- `{attacked_player}` - 被攻击者名称

### 颜色代码
支持使用 `&` 符号的颜色代码：
- `&c` = 红色
- `&a` = 绿色
- `&e` = 黄色
- `&b` = 青色
- 等等...

## 技术信息

- **版本**: 1.0.0
- **兼容性**: Paper 1.8.8
- **依赖**: Spigot API 1.8.8-R0.1-SNAPSHOT
- **Java版本**: 8+

## 开发信息

### 项目结构
```
src/main/java/org/example/
├── OECPlugin.java              # 主插件类
├── commands/OECCommand.java    # 指令处理
├── managers/                   # 管理器包
│   ├── CombatManager.java      # 战斗状态管理
│   ├── MessageManager.java     # 消息处理
│   └── ConfigManager.java      # 配置管理
├── listeners/CombatListener.java # 事件监听
└── data/CombatData.java        # 数据结构
```

### 构建项目
```bash
mvn clean compile
jar -cvf OECPlugin-1.0.0.jar -C target/classes .
```

## 许可证

本项目使用 MIT 许可证。

## 作者

MuXi - 专业的Minecraft插件开发者

---

如有问题或建议，请联系开发者。
