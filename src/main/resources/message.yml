# OEC插件消息配置文件
# 支持颜色代码：使用 & 符号，例如 &c = 红色，&a = 绿色
# 支持的变量：
# {fight_time} - 剩余战斗时间（秒）
# {killer} - 攻击者名称
# {attacked_player} - 被攻击者名称

messages:
  # 战斗状态中无法打开末影箱的提示消息
  combat-deny: "&c您当前{fight_time}秒内无法打开末影箱，因为你处于战斗状态"

  # 成功打开末影箱的消息
  enderchest-opened: "&a末影箱已打开"

  # 指令使用错误提示
  command-usage: "&c使用方法: /oec"

  # 权限不足提示
  no-permission: "&c你没有权限使用这个指令！"

  # 插件重载成功消息
  reload-success: "&aOEC插件配置已重载！"

  # 插件重载失败消息
  reload-failed: "&c插件配置重载失败，请检查配置文件格式！"

# 战斗系统配置
combat:
  # 战斗状态持续时间（秒）
  duration: 10

  # 是否在控制台输出战斗日志
  log-combat: true

  # 是否显示战斗开始消息
  show-combat-start: false