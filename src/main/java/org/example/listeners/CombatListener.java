package org.example.listeners;

import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.example.managers.CombatManager;

/**
 * 战斗事件监听器
 */
public class CombatListener implements Listener {
    
    private final CombatManager combatManager;
    
    public CombatListener(CombatManager combatManager) {
        this.combatManager = combatManager;
    }
    
    /**
     * 监听实体伤害事件，检测玩家之间的战斗
     * @param event 实体伤害事件
     */
    @EventHandler(priority = EventPriority.MONITOR, ignoreCancelled = true)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        Entity damager = event.getDamager();
        Entity damaged = event.getEntity();
        
        // 检查是否为玩家之间的战斗
        if (!(damager instanceof Player) || !(damaged instanceof Player)) {
            return;
        }
        
        Player attacker = (Player) damager;
        Player victim = (Player) damaged;
        
        // 确保攻击者和被攻击者不是同一个人
        if (attacker.equals(victim)) {
            return;
        }
        
        // 设置攻击者的战斗状态
        combatManager.setCombat(attacker, attacker.getName(), victim.getName());
        
        // 设置被攻击者的战斗状态
        combatManager.setCombat(victim, attacker.getName(), victim.getName());
    }
    
    /**
     * 监听玩家退出事件，清理战斗状态
     * @param event 玩家退出事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        
        // 玩家退出时清理其战斗状态
        combatManager.removeCombat(player);
    }
}
