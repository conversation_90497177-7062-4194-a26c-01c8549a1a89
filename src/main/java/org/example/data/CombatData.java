package org.example.data;

/**
 * 战斗数据类，用于存储玩家的战斗状态信息
 */
public class CombatData {
    
    private final long combatEndTime;
    private final String killer;
    private final String attackedPlayer;
    
    /**
     * 构造函数
     * @param combatEndTime 战斗状态结束时间（毫秒时间戳）
     * @param killer 攻击者名称
     * @param attackedPlayer 被攻击者名称
     */
    public CombatData(long combatEndTime, String killer, String attackedPlayer) {
        this.combatEndTime = combatEndTime;
        this.killer = killer;
        this.attackedPlayer = attackedPlayer;
    }
    
    /**
     * 获取战斗状态结束时间
     * @return 毫秒时间戳
     */
    public long getCombatEndTime() {
        return combatEndTime;
    }
    
    /**
     * 获取攻击者名称
     * @return 攻击者名称
     */
    public String getKiller() {
        return killer;
    }
    
    /**
     * 获取被攻击者名称
     * @return 被攻击者名称
     */
    public String getAttackedPlayer() {
        return attackedPlayer;
    }
    
    /**
     * 检查战斗状态是否已过期
     * @return true如果战斗状态已过期，false如果仍在战斗中
     */
    public boolean isExpired() {
        return System.currentTimeMillis() >= combatEndTime;
    }
    
    /**
     * 获取剩余战斗时间（秒）
     * @return 剩余秒数，如果已过期则返回0
     */
    public int getRemainingSeconds() {
        long remaining = combatEndTime - System.currentTimeMillis();
        return remaining > 0 ? (int) Math.ceil(remaining / 1000.0) : 0;
    }
    
    @Override
    public String toString() {
        return "CombatData{" +
                "combatEndTime=" + combatEndTime +
                ", killer='" + killer + '\'' +
                ", attackedPlayer='" + attackedPlayer + '\'' +
                ", remainingSeconds=" + getRemainingSeconds() +
                '}';
    }
}
