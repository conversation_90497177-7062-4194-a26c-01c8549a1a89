package org.example.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.example.data.CombatData;
import org.example.managers.CombatManager;
import org.example.managers.MessageManager;

/**
 * OEC指令处理器
 */
public class OECCommand implements CommandExecutor {
    
    private final CombatManager combatManager;
    private final MessageManager messageManager;
    
    public OECCommand(CombatManager combatManager, MessageManager messageManager) {
        this.combatManager = combatManager;
        this.messageManager = messageManager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 检查是否为玩家执行指令
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c这个指令只能由玩家执行！");
            return true;
        }
        
        Player player = (Player) sender;
        
        // 检查权限
        if (!player.hasPermission("oec.use")) {
            messageManager.sendNoPermissionMessage(player);
            return true;
        }
        
        // 检查参数数量
        if (args.length > 0) {
            messageManager.sendCommandUsageMessage(player);
            return true;
        }
        
        // 检查是否处于战斗状态
        if (combatManager.isInCombat(player)) {
            CombatData combatData = combatManager.getCombatData(player);
            messageManager.sendCombatDenyMessage(player, combatData);
            return true;
        }
        
        // 打开末影箱
        try {
            player.openInventory(player.getEnderChest());
            messageManager.sendEnderchestOpenedMessage(player);
        } catch (Exception e) {
            player.sendMessage("§c打开末影箱时发生错误，请稍后再试！");
            e.printStackTrace();
        }
        
        return true;
    }
}
