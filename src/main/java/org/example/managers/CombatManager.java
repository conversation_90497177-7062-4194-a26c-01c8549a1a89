package org.example.managers;

import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.example.data.CombatData;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

/**
 * 战斗状态管理器
 */
public class CombatManager {
    
    private static CombatManager instance;
    private final JavaPlugin plugin;
    private final ConfigManager configManager;
    private final Map<UUID, CombatData> combatPlayers;
    
    private CombatManager(JavaPlugin plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
        this.combatPlayers = new HashMap<>();
        startCleanupTask();
    }
    
    /**
     * 获取单例实例
     * @param plugin 插件实例
     * @param configManager 配置管理器
     * @return CombatManager实例
     */
    public static CombatManager getInstance(JavaPlugin plugin, ConfigManager configManager) {
        if (instance == null) {
            instance = new CombatManager(plugin, configManager);
        }
        return instance;
    }
    
    /**
     * 获取单例实例（已初始化后使用）
     * @return CombatManager实例
     */
    public static CombatManager getInstance() {
        return instance;
    }
    
    /**
     * 将玩家设置为战斗状态
     * @param player 玩家
     * @param killer 攻击者名称
     * @param attackedPlayer 被攻击者名称
     */
    public void setCombat(Player player, String killer, String attackedPlayer) {
        if (player == null) return;
        
        long combatDuration = configManager.getCombatDuration() * 1000L; // 转换为毫秒
        long combatEndTime = System.currentTimeMillis() + combatDuration;
        
        CombatData combatData = new CombatData(combatEndTime, killer, attackedPlayer);
        combatPlayers.put(player.getUniqueId(), combatData);
        
        // 如果配置允许，记录战斗日志
        if (configManager.getMessageConfig().getBoolean("combat.log-combat", true)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 进入战斗状态 (攻击者: " + killer + ", 被攻击者: " + attackedPlayer + ")");
        }
    }
    
    /**
     * 检查玩家是否处于战斗状态
     * @param player 玩家
     * @return true如果处于战斗状态，false如果不在战斗中
     */
    public boolean isInCombat(Player player) {
        if (player == null) return false;
        
        CombatData combatData = combatPlayers.get(player.getUniqueId());
        if (combatData == null) return false;
        
        if (combatData.isExpired()) {
            removeCombat(player);
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取玩家的战斗数据
     * @param player 玩家
     * @return 战斗数据，如果不在战斗中则返回null
     */
    public CombatData getCombatData(Player player) {
        if (player == null) return null;
        
        CombatData combatData = combatPlayers.get(player.getUniqueId());
        if (combatData != null && combatData.isExpired()) {
            removeCombat(player);
            return null;
        }
        
        return combatData;
    }
    
    /**
     * 移除玩家的战斗状态
     * @param player 玩家
     */
    public void removeCombat(Player player) {
        if (player == null) return;
        
        combatPlayers.remove(player.getUniqueId());
        
        if (configManager.getMessageConfig().getBoolean("combat.log-combat", true)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 退出战斗状态");
        }
    }
    
    /**
     * 获取战斗中的玩家数量
     * @return 战斗中的玩家数量
     */
    public int getCombatPlayersCount() {
        return combatPlayers.size();
    }
    
    /**
     * 清理所有战斗状态
     */
    public void clearAllCombat() {
        combatPlayers.clear();
        plugin.getLogger().info("已清理所有战斗状态");
    }
    
    /**
     * 启动定期清理任务
     */
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                cleanupExpiredCombat();
            }
        }.runTaskTimer(plugin, 20L * 5, 20L * 5); // 每5秒执行一次清理
    }
    
    /**
     * 清理过期的战斗状态
     */
    private void cleanupExpiredCombat() {
        Iterator<Map.Entry<UUID, CombatData>> iterator = combatPlayers.entrySet().iterator();
        int cleanedCount = 0;
        
        while (iterator.hasNext()) {
            Map.Entry<UUID, CombatData> entry = iterator.next();
            if (entry.getValue().isExpired()) {
                iterator.remove();
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0 && configManager.getMessageConfig().getBoolean("combat.log-combat", true)) {
            plugin.getLogger().info("清理了 " + cleanedCount + " 个过期的战斗状态");
        }
    }
}
