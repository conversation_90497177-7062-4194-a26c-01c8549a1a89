package org.example.managers;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.logging.Level;

/**
 * 配置文件管理器
 */
public class ConfigManager {
    
    private final JavaPlugin plugin;
    private FileConfiguration messageConfig;
    private File messageFile;
    
    public ConfigManager(JavaPlugin plugin) {
        this.plugin = plugin;
        setupMessageConfig();
    }
    
    /**
     * 设置消息配置文件
     */
    private void setupMessageConfig() {
        messageFile = new File(plugin.getDataFolder(), "message.yml");
        
        if (!messageFile.exists()) {
            messageFile.getParentFile().mkdirs();
            plugin.saveResource("message.yml", false);
        }
        
        messageConfig = YamlConfiguration.loadConfiguration(messageFile);
        
        // 加载默认配置作为备用
        InputStream defConfigStream = plugin.getResource("message.yml");
        if (defConfigStream != null) {
            YamlConfiguration defConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defConfigStream));
            messageConfig.setDefaults(defConfig);
        }
    }
    
    /**
     * 获取消息配置
     * @return 消息配置对象
     */
    public FileConfiguration getMessageConfig() {
        if (messageConfig == null) {
            setupMessageConfig();
        }
        return messageConfig;
    }
    
    /**
     * 保存消息配置
     */
    public void saveMessageConfig() {
        if (messageConfig == null || messageFile == null) {
            return;
        }
        try {
            getMessageConfig().save(messageFile);
        } catch (IOException ex) {
            plugin.getLogger().log(Level.SEVERE, "无法保存消息配置文件到 " + messageFile, ex);
        }
    }
    
    /**
     * 重新加载消息配置
     */
    public void reloadMessageConfig() {
        messageConfig = YamlConfiguration.loadConfiguration(messageFile);
        
        InputStream defConfigStream = plugin.getResource("message.yml");
        if (defConfigStream != null) {
            YamlConfiguration defConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defConfigStream));
            messageConfig.setDefaults(defConfig);
        }
    }
    
    /**
     * 获取战斗持续时间配置
     * @return 战斗持续时间（秒）
     */
    public int getCombatDuration() {
        return getMessageConfig().getInt("combat.duration", 10);
    }
    
    /**
     * 获取消息文本
     * @param path 配置路径
     * @param defaultValue 默认值
     * @return 消息文本
     */
    public String getMessage(String path, String defaultValue) {
        return getMessageConfig().getString("messages." + path, defaultValue);
    }
    
    /**
     * 获取消息文本（带颜色代码转换）
     * @param path 配置路径
     * @param defaultValue 默认值
     * @return 转换颜色代码后的消息文本
     */
    public String getColoredMessage(String path, String defaultValue) {
        String message = getMessage(path, defaultValue);
        return message.replace("&", "§");
    }
}
