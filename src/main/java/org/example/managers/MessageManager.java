package org.example.managers;

import org.bukkit.entity.Player;
import org.example.data.CombatData;

/**
 * 消息管理器，负责处理消息变量替换和发送
 */
public class MessageManager {
    
    private final ConfigManager configManager;
    
    public MessageManager(ConfigManager configManager) {
        this.configManager = configManager;
    }
    
    /**
     * 向玩家发送战斗拒绝消息
     * @param player 玩家
     * @param combatData 战斗数据
     */
    public void sendCombatDenyMessage(Player player, CombatData combatData) {
        String message = configManager.getColoredMessage("combat-deny", 
            "&c您当前{fight_time}秒内无法打开末影箱，因为你处于战斗状态");
        
        // 替换变量
        message = replaceVariables(message, combatData);
        
        // 发送到屏幕中央（使用Title API）
        sendTitle(player, "", message, 10, 40, 10);
    }
    
    /**
     * 向玩家发送末影箱打开成功消息
     * @param player 玩家
     */
    public void sendEnderchestOpenedMessage(Player player) {
        String message = configManager.getColoredMessage("enderchest-opened", "&a末影箱已打开");
        player.sendMessage(message);
    }
    
    /**
     * 向玩家发送指令使用方法消息
     * @param player 玩家
     */
    public void sendCommandUsageMessage(Player player) {
        String message = configManager.getColoredMessage("command-usage", "&c使用方法: /oec");
        player.sendMessage(message);
    }
    
    /**
     * 向玩家发送权限不足消息
     * @param player 玩家
     */
    public void sendNoPermissionMessage(Player player) {
        String message = configManager.getColoredMessage("no-permission", "&c你没有权限使用这个指令！");
        player.sendMessage(message);
    }
    
    /**
     * 替换消息中的变量
     * @param message 原始消息
     * @param combatData 战斗数据
     * @return 替换变量后的消息
     */
    private String replaceVariables(String message, CombatData combatData) {
        if (combatData == null) {
            return message;
        }
        
        // 替换 {fight_time} 变量
        message = message.replace("{fight_time}", String.valueOf(combatData.getRemainingSeconds()));
        
        // 替换 {killer} 变量
        if (combatData.getKiller() != null) {
            message = message.replace("{killer}", combatData.getKiller());
        }
        
        // 替换 {attacked_player} 变量
        if (combatData.getAttackedPlayer() != null) {
            message = message.replace("{attacked_player}", combatData.getAttackedPlayer());
        }
        
        return message;
    }
    
    /**
     * 发送Title消息到玩家屏幕中央
     * 兼容1.8.8版本的Title发送方法
     * @param player 玩家
     * @param title 主标题
     * @param subtitle 副标题
     * @param fadeIn 淡入时间（tick）
     * @param stay 停留时间（tick）
     * @param fadeOut 淡出时间（tick）
     */
    private void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            // 1.8.8版本使用反射发送Title
            Class<?> chatComponentTextClass = getNMSClass("ChatComponentText");
            Class<?> packetPlayOutTitleClass = getNMSClass("PacketPlayOutTitle");
            Class<?> enumTitleActionClass = getNMSClass("PacketPlayOutTitle$EnumTitleAction");
            
            Object chatTitle = chatComponentTextClass.getConstructor(String.class).newInstance(title);
            Object chatSubtitle = chatComponentTextClass.getConstructor(String.class).newInstance(subtitle);
            
            Object enumTitleAction = enumTitleActionClass.getField("TITLE").get(null);
            Object enumSubtitleAction = enumTitleActionClass.getField("SUBTITLE").get(null);
            Object enumTimesAction = enumTitleActionClass.getField("TIMES").get(null);
            
            Object titlePacket = packetPlayOutTitleClass.getConstructor(enumTitleActionClass, 
                getNMSClass("IChatBaseComponent"), int.class, int.class, int.class)
                .newInstance(enumTitleAction, chatTitle, fadeIn, stay, fadeOut);
                
            Object subtitlePacket = packetPlayOutTitleClass.getConstructor(enumTitleActionClass, 
                getNMSClass("IChatBaseComponent"))
                .newInstance(enumSubtitleAction, chatSubtitle);
                
            Object timesPacket = packetPlayOutTitleClass.getConstructor(enumTitleActionClass, 
                getNMSClass("IChatBaseComponent"), int.class, int.class, int.class)
                .newInstance(enumTimesAction, null, fadeIn, stay, fadeOut);
            
            sendPacket(player, timesPacket);
            sendPacket(player, titlePacket);
            sendPacket(player, subtitlePacket);
            
        } catch (Exception e) {
            // 如果Title发送失败，降级为普通聊天消息
            player.sendMessage(subtitle);
        }
    }
    
    /**
     * 获取NMS类
     * @param className 类名
     * @return NMS类
     */
    private Class<?> getNMSClass(String className) throws ClassNotFoundException {
        String version = org.bukkit.Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
        return Class.forName("net.minecraft.server." + version + "." + className);
    }
    
    /**
     * 发送数据包给玩家
     * @param player 玩家
     * @param packet 数据包
     */
    private void sendPacket(Player player, Object packet) throws Exception {
        Object handle = player.getClass().getMethod("getHandle").invoke(player);
        Object playerConnection = handle.getClass().getField("playerConnection").get(handle);
        playerConnection.getClass().getMethod("sendPacket", getNMSClass("Packet")).invoke(playerConnection, packet);
    }
}
