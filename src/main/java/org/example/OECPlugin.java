package org.example;

import org.bukkit.plugin.java.JavaPlugin;
import org.example.commands.OECCommand;
import org.example.listeners.CombatListener;
import org.example.managers.CombatManager;
import org.example.managers.ConfigManager;
import org.example.managers.MessageManager;

/**
 * OEC插件主类
 * 功能：允许玩家使用/OEC指令打开末影箱，但在战斗状态下会被阻止
 */
public class OECPlugin extends JavaPlugin {
    
    private ConfigManager configManager;
    private CombatManager combatManager;
    private MessageManager messageManager;
    
    @Override
    public void onEnable() {
        // 插件启动日志
        getLogger().info("OEC插件正在启动...");
        
        // 初始化管理器
        initializeManagers();
        
        // 注册指令
        registerCommands();
        
        // 注册事件监听器
        registerListeners();
        
        // 启动完成日志
        getLogger().info("OEC插件启动完成！");
        getLogger().info("玩家可以使用 /oec 指令打开末影箱");
        getLogger().info("战斗状态持续时间: " + configManager.getCombatDuration() + " 秒");
    }
    
    @Override
    public void onDisable() {
        // 插件关闭时清理资源
        if (combatManager != null) {
            combatManager.clearAllCombat();
        }
        
        getLogger().info("OEC插件已关闭！");
    }
    
    /**
     * 初始化所有管理器
     */
    private void initializeManagers() {
        try {
            // 初始化配置管理器
            configManager = new ConfigManager(this);
            getLogger().info("配置管理器初始化完成");
            
            // 初始化战斗管理器
            combatManager = CombatManager.getInstance(this, configManager);
            getLogger().info("战斗管理器初始化完成");
            
            // 初始化消息管理器
            messageManager = new MessageManager(configManager);
            getLogger().info("消息管理器初始化完成");
            
        } catch (Exception e) {
            getLogger().severe("管理器初始化失败: " + e.getMessage());
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
        }
    }
    
    /**
     * 注册指令
     */
    private void registerCommands() {
        try {
            // 注册 /oec 指令
            OECCommand oecCommand = new OECCommand(combatManager, messageManager);
            getCommand("oec").setExecutor(oecCommand);
            
            getLogger().info("指令注册完成");
            
        } catch (Exception e) {
            getLogger().severe("指令注册失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 注册事件监听器
     */
    private void registerListeners() {
        try {
            // 注册战斗事件监听器
            CombatListener combatListener = new CombatListener(combatManager);
            getServer().getPluginManager().registerEvents(combatListener, this);
            
            getLogger().info("事件监听器注册完成");
            
        } catch (Exception e) {
            getLogger().severe("事件监听器注册失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取配置管理器
     * @return 配置管理器实例
     */
    public ConfigManager getConfigManager() {
        return configManager;
    }
    
    /**
     * 获取战斗管理器
     * @return 战斗管理器实例
     */
    public CombatManager getCombatManager() {
        return combatManager;
    }
    
    /**
     * 获取消息管理器
     * @return 消息管理器实例
     */
    public MessageManager getMessageManager() {
        return messageManager;
    }
    
    /**
     * 重载插件配置
     */
    public void reloadPluginConfig() {
        try {
            configManager.reloadMessageConfig();
            getLogger().info("插件配置重载成功！");
        } catch (Exception e) {
            getLogger().severe("插件配置重载失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
